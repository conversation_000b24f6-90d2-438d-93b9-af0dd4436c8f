import React from 'react';
import { Box, Text } from 'ink';
import Gradient from 'ink-gradient';
// import { longAsciiLogo } from '../components/AsciiArt.js';
import BigText from 'ink-big-text';
import { defaultTheme } from '../themes/color.js';
import ContentInput from '../components/ContentInput.js';



type Props = {
	/** 用户名称 */
	name: string;
	/** 导航函数 */
	onNavigate: (route: string) => void;
};


/**
 * 主页组件
 * 显示欢迎信息和主要功能入口
 */
export default function HomePage({ }: Props) {


	return (
		<Box flexDirection="column" padding={1}>
			{/* 顶部 ASCII 艺术 */}
			<Gradient colors={defaultTheme.GradientColors}>
				{/* <Text>{longAsciiLogo}</Text> */}
				<BigText text="guijimada" />
			</Gradient>

			{/* 欢迎信息 */}
			<Box flexDirection="column">
				<Text color={defaultTheme.Foreground}>Tips for getting started:</Text>
				<Text color={defaultTheme.Foreground}>
					1. Ask questions, edit files, or run commands.
				</Text>
				<Text color={defaultTheme.Foreground}>
					2. Be specific for the best results.
				</Text>
				<Text color={defaultTheme.Foreground}>
					3. Create{' '}
					<Text bold color={defaultTheme.AccentPurple}>
						硅基马达.md
					</Text>{' '}
					files to customize your interactions with 硅基马达.
				</Text>
				<Text color={defaultTheme.Foreground}>
					<Text bold color={defaultTheme.AccentPurple}>
						/help
					</Text>{' '}
					for more information.
				</Text>
			</Box>

			<ContentInput />

		</Box>
	);
}
