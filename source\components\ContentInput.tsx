import { Box, Text } from "ink";
import React, { useState } from "react";
import TextInput from 'ink-text-input';
import { defaultTheme } from '../themes/color.js';



type Props = {

};

export default function ContentInput({ }: Props) {


	const placeholder = "Type your message or @path/to/file";

	// 输入框输入
	const [query, setQuery] = useState('');

	return (
		<>
			{/* 输入框 */}
			<Box
				borderStyle="round"
				borderColor={defaultTheme.AccentPurple}
				paddingX={1}
				marginTop={1}
			>
				<Text color={"#f0c6b8"}>
					{'> '}
				</Text>

				<TextInput value={query} onChange={setQuery} placeholder={placeholder} />
			</Box >
		</>
	)

}
