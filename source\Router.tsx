import React, { useState } from 'react';
import HomePage from './pages/HomePage.js';

type Props = {
	/** 用户名称，从命令行参数获取 */
	name: string;
	/** 初始页面，从命令行参数获取 */
	initialPage: string;
};

type Route = 'home' | 'settings' | 'help';

/**
 * 路由组件
 * 负责管理页面导航和状态
 * 根据当前路由渲染对应的页面组件
 */
export default function Router({ name, initialPage }: Props) {
	// 验证初始页面参数，如果无效则默认为 'home'
	const validRoutes: Route[] = ['home', 'settings', 'help'];
	const validInitialPage = validRoutes.includes(initialPage as Route)
		? (initialPage as Route)
		: 'home';

	// 当前路由状态
	const [currentRoute, setCurrentRoute] = useState<Route>(validInitialPage);

	/**
	 * 导航函数
	 * 用于在不同页面之间切换
	 */
	const handleNavigate = (route: string) => {
		if (validRoutes.includes(route as Route)) {
			setCurrentRoute(route as Route);
		}
	};

	// 根据当前路由渲染对应的页面组件
	switch (currentRoute) {
		case 'home': {
			return <HomePage name={name} onNavigate={handleNavigate} />;
		}

		default: {
			// 如果路由无效，回退到主页
			return <HomePage name={name} onNavigate={handleNavigate} />;
		}
	}
}
