#!/usr/bin/env node
/**
 * CLI 入口文件
 * 负责解析命令行参数并启动 Ink 应用
 *
 * 执行方式：
 * 1. 开发模式: npx tsx source/cli.tsx --name=张三
 * 2. 全局安装: npm install -g . && my-ink-cli --name=张三
 * 3. 本地测试: npm run dev -- --name=张三
 */

import React from 'react';
import {render} from 'ink';
import meow from 'meow';
import App from './app.js';

/**
 * meow CLI 配置
 * 使用模板字符串定义帮助文档和参数规则
 */
const cli = meow(
	`
	Usage
	  $ my-ink-cli

	Options
		--name  Your name
		--page  Initial page to display (home|settings|help)

	Examples
	  $ my-ink-cli --name=Jane
	  Hello, Jane

	  $ my-ink-cli --page=settings
	  Open settings page directly
`,
	{
		importMeta: import.meta,
		flags: {
			/**
			 * 用户名称参数
			 * 用于个性化问候语
			 * 类型: 字符串
			 * 默认值: 'Stranger' (在 App 组件中定义)
			 */
			name: {
				type: 'string',
			},
			/**
			 * 初始页面参数
			 * 用于直接跳转到指定页面
			 * 类型: 字符串
			 * 可选值: 'home' | 'settings' | 'help'
			 * 默认值: 'home' (在 Router 组件中定义)
			 */
			page: {
				type: 'string',
			},
		},
	},
);

/**
 * 渲染主应用组件
 * 将命令行参数传递给 React 组件
 * 使用 Ink 的 render 方法在终端中渲染 React 应用
 * 新增: 传递 page 参数支持路由跳转
 */
render(<App name={cli.flags.name} initialPage={cli.flags.page} />);
